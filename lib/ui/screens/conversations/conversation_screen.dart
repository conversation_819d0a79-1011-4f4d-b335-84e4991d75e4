import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/frameworks/presence/presence_event.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_state.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_state.dart';
import 'package:x1440/ui/modals/end_all_conversations_modal.dart';
import 'package:x1440/ui/screens/presence/presence_request_listener.dart';
import 'package:x1440/ui/widgets/alerts/alerts_widget.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'widgets/conversation_screen_appbar.dart';
import 'conversation_screen_body.dart';

class ConversationScreen extends StatefulWidget {
  const ConversationScreen({super.key});

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen>
    with WidgetsBindingObserver {
  DateTime? _lastResumeTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Only process if at least 5 seconds have passed since last resume
      final now = DateTime.now();
      if (_lastResumeTime == null ||
          now.difference(_lastResumeTime!) > const Duration(seconds: 2)) {
        _lastResumeTime = now;
        final bloc = GetIt.I<ConversationsBloc>();
        bloc.add(GetDeviceTokenPermissionsEvent());
      }
    }
  }

  @override
  Widget build(BuildContext context) => PresenceRequestListener(
        child: BlocProvider.value(
          value: GetIt.I<ContactsBloc>(),
          child: BlocProvider.value(
            value: () {
              final bloc = GetIt.I<ConversationsBloc>();
              bloc.add(InitEvent());

              /// when app opens from a tapped notification, the 'goToMessaggingEndUser' event is set before this listener has loaded, so we need to catch that possibility & send the event again here
              SfId? goToMessagingEndUserId =
                  bloc.state.goToMessagingEndUserId?.consume();
              if (goToMessagingEndUserId != null) {
                bloc.add(GoToConversationEvent(goToMessagingEndUserId));
              }

              return bloc;
            }(),
            child: BlocProvider.value(
              value: GetIt.I<AppErrorBloc>(),
              child: MultiBlocListener(
                listeners: [
                  BlocListener<ConversationsBloc, ConversationsState>(
                    listener: (context, state) {
                      SfId? goToMessagingEndUserId =
                          state.goToMessagingEndUserId?.consume();
                      if (goToMessagingEndUserId != null) {
                        GetIt.I<LoggingUseCase>()
                            .getRemoteLogger('ConversationScreen')
                            .info(
                                '🔔 NOTIFICATION_ROUTING - ConversationScreen listener: going to chat screen for conversation id: $goToMessagingEndUserId');
                        GetIt.I<RoutingManager>()
                            .goToChatScreen(goToMessagingEndUserId, null);
                      } else {
                        GetIt.I<LoggingUseCase>()
                            .getRemoteLogger('ConversationScreen')
                            .info(
                                '🔔 NOTIFICATION_ROUTING - ConversationScreen listener: goToMessagingEndUserId is null');
                      }
                      PostEndConversationsEvent? postEndConversationsEvent =
                          state.showEndAllConversationsConfirmation?.consume();
                      if (postEndConversationsEvent != null) {
                        confirmEndConversationsAndLogout(
                                context: context,
                                event: postEndConversationsEvent)
                            .then((value) {
                          if (value == true) {
                            if (postEndConversationsEvent ==
                                PostEndConversationsEvent.logout) {
                              GetIt.I<AuthBloc>().add(LogoutEvent());
                            } else {
                              GetIt.I<PresenceManager>()
                                  .add(UpdatePresenceStatusEvent());
                            }
                          }
                        });
                      }
                    },
                  ),
                  BlocListener<AppErrorBloc, AppErrorState>(
                    listener: (context, appErrorState) {
                      if (appErrorState.showError?.consume() != null &&
                          appErrorState.activeError != null) {
                        Utils.showAlertToast(
                            AlertsWidget(
                                alertType: AlertsWidgetType.warning,
                                title: S.of(context).warning_title,
                                message: appErrorState.activeError.toString()),
                            context);
                      }
                    },
                  ),
                ],
                child: GestureDetector(
                  onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                  child: Scaffold(
                    appBar: ConversationsScreenAppBar(),
                    body: const ConversationScreenBody(),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}
