import 'package:freezed_annotation/freezed_annotation.dart';

import 'image_size.dart';

part 'image_size_state.freezed.dart';
part 'image_size_state.g.dart';

@freezed
abstract class ImageSizeState with _$ImageSizeState {
  const factory ImageSizeState({
        @Default(<String, ImageSize>{}) Map<String, ImageSize> imageSizes
  }) = _ImageSizeState;

  factory ImageSizeState.fromJson(Map<String, dynamic> json) => _$ImageSizeStateFromJson(json);
}