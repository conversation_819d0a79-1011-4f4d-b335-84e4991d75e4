import 'package:freezed_annotation/freezed_annotation.dart';

part 'refresh_salesforce_token_response.freezed.dart';
part 'refresh_salesforce_token_response.g.dart';

@freezed
abstract class RefreshSalesforceTokenResponse with _$RefreshSalesforceTokenResponse {
  const factory RefreshSalesforceTokenResponse({
    // Success response fields
    String? accessToken,
    String? refreshToken,

    // Error response fields
    String? code,
    String? message,
    String? errorMessage,
  }) = _RefreshSalesforceTokenResponse;

  factory RefreshSalesforceTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$RefreshSalesforceTokenResponseFromJson(json);
}
