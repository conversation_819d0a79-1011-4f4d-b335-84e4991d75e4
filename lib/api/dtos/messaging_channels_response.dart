import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';

part 'messaging_channels_response.freezed.dart';
part 'messaging_channels_response.g.dart';

@freezed
abstract class MessagingChannelsResponse with _$MessagingChannelsResponse {
  const MessagingChannelsResponse._();
  const factory MessagingChannelsResponse({
    required List<MessagingChannelEntry> channelSelectionEntries,
    required List<MessagingChannelEntry> otherChannels,
  }) = _MessagingChannelsResponse;

  factory MessagingChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelsResponseFromJson(json);
}
