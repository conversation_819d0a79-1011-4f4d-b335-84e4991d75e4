// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_salesforce_token_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefreshSalesforceTokenResponse {
// Success response fields
  String? get accessToken;
  String? get refreshToken; // Error response fields
  String? get code;
  String? get message;
  String? get errorMessage;

  /// Create a copy of RefreshSalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshSalesforceTokenResponseCopyWith<RefreshSalesforceTokenResponse>
      get copyWith => _$RefreshSalesforceTokenResponseCopyWithImpl<
              RefreshSalesforceTokenResponse>(
          this as RefreshSalesforceTokenResponse, _$identity);

  /// Serializes this RefreshSalesforceTokenResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshSalesforceTokenResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accessToken, refreshToken, code, message, errorMessage);

  @override
  String toString() {
    return 'RefreshSalesforceTokenResponse(accessToken: $accessToken, refreshToken: $refreshToken, code: $code, message: $message, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $RefreshSalesforceTokenResponseCopyWith<$Res> {
  factory $RefreshSalesforceTokenResponseCopyWith(
          RefreshSalesforceTokenResponse value,
          $Res Function(RefreshSalesforceTokenResponse) _then) =
      _$RefreshSalesforceTokenResponseCopyWithImpl;
  @useResult
  $Res call(
      {String? accessToken,
      String? refreshToken,
      String? code,
      String? message,
      String? errorMessage});
}

/// @nodoc
class _$RefreshSalesforceTokenResponseCopyWithImpl<$Res>
    implements $RefreshSalesforceTokenResponseCopyWith<$Res> {
  _$RefreshSalesforceTokenResponseCopyWithImpl(this._self, this._then);

  final RefreshSalesforceTokenResponse _self;
  final $Res Function(RefreshSalesforceTokenResponse) _then;

  /// Create a copy of RefreshSalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? refreshToken = freezed,
    Object? code = freezed,
    Object? message = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _self.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [RefreshSalesforceTokenResponse].
extension RefreshSalesforceTokenResponsePatterns
    on RefreshSalesforceTokenResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RefreshSalesforceTokenResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RefreshSalesforceTokenResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RefreshSalesforceTokenResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? accessToken, String? refreshToken, String? code,
            String? message, String? errorMessage)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse() when $default != null:
        return $default(_that.accessToken, _that.refreshToken, _that.code,
            _that.message, _that.errorMessage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? accessToken, String? refreshToken, String? code,
            String? message, String? errorMessage)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse():
        return $default(_that.accessToken, _that.refreshToken, _that.code,
            _that.message, _that.errorMessage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? accessToken, String? refreshToken, String? code,
            String? message, String? errorMessage)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenResponse() when $default != null:
        return $default(_that.accessToken, _that.refreshToken, _that.code,
            _that.message, _that.errorMessage);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshSalesforceTokenResponse
    implements RefreshSalesforceTokenResponse {
  const _RefreshSalesforceTokenResponse(
      {this.accessToken,
      this.refreshToken,
      this.code,
      this.message,
      this.errorMessage});
  factory _RefreshSalesforceTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$RefreshSalesforceTokenResponseFromJson(json);

// Success response fields
  @override
  final String? accessToken;
  @override
  final String? refreshToken;
// Error response fields
  @override
  final String? code;
  @override
  final String? message;
  @override
  final String? errorMessage;

  /// Create a copy of RefreshSalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshSalesforceTokenResponseCopyWith<_RefreshSalesforceTokenResponse>
      get copyWith => __$RefreshSalesforceTokenResponseCopyWithImpl<
          _RefreshSalesforceTokenResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshSalesforceTokenResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshSalesforceTokenResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accessToken, refreshToken, code, message, errorMessage);

  @override
  String toString() {
    return 'RefreshSalesforceTokenResponse(accessToken: $accessToken, refreshToken: $refreshToken, code: $code, message: $message, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$RefreshSalesforceTokenResponseCopyWith<$Res>
    implements $RefreshSalesforceTokenResponseCopyWith<$Res> {
  factory _$RefreshSalesforceTokenResponseCopyWith(
          _RefreshSalesforceTokenResponse value,
          $Res Function(_RefreshSalesforceTokenResponse) _then) =
      __$RefreshSalesforceTokenResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? accessToken,
      String? refreshToken,
      String? code,
      String? message,
      String? errorMessage});
}

/// @nodoc
class __$RefreshSalesforceTokenResponseCopyWithImpl<$Res>
    implements _$RefreshSalesforceTokenResponseCopyWith<$Res> {
  __$RefreshSalesforceTokenResponseCopyWithImpl(this._self, this._then);

  final _RefreshSalesforceTokenResponse _self;
  final $Res Function(_RefreshSalesforceTokenResponse) _then;

  /// Create a copy of RefreshSalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? accessToken = freezed,
    Object? refreshToken = freezed,
    Object? code = freezed,
    Object? message = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_RefreshSalesforceTokenResponse(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _self.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
