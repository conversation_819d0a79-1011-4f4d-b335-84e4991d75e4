import 'package:freezed_annotation/freezed_annotation.dart';

part 'presence_status.freezed.dart';

part 'presence_status.g.dart';

enum PresenceStatusOption { online, offline, busy }

// Helper function for enum serialization
PresenceStatusOption _presenceStatusOptionFromJson(String value) {
  return PresenceStatusOption.values.firstWhere(
    (e) => e.toString().split('.').last == value,
    orElse: () => PresenceStatusOption.offline,
  );
}

String _presenceStatusOptionToJson(PresenceStatusOption option) {
  return option.toString().split('.').last;
}

@freezed
abstract class PresenceStatus with _$PresenceStatus {
  // Id get isarId => fastHash(id);

  const PresenceStatus._();

  // @ignore
  bool get isOnline =>
      statusOption == PresenceStatusOption.online ||
      statusOption == PresenceStatusOption.busy;

  const factory PresenceStatus({
  required String id,
  required String label,
      @JsonKey(
        fromJson: _presenceStatusOptionFromJson,
        toJson: _presenceStatusOptionToJson,
      )
      required PresenceStatusOption statusOption}) = _PresenceStatus;

  factory PresenceStatus.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusFromJson(json);
}
