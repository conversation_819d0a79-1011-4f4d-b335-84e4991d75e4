import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/outbound_message_body.dart';

part 'outbound_messages_body.freezed.dart';
part 'outbound_messages_body.g.dart';

@freezed
abstract class OutboundMessagesBody with _$OutboundMessagesBody {
  const factory OutboundMessagesBody({
    required List<OutboundMessageBody> messages,
  }) = _OutboundMessagesBody;

  factory OutboundMessagesBody.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagesBodyFromJson(json);
}
